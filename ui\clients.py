from PyQt5.QtWidgets import (Q<PERSON><PERSON><PERSON>, QVBox<PERSON>ayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QDateEdit, QDoubleSpinBox,
                            QFileDialog, QMenu, QAction, QSizePolicy, QFrame,
                            QTextBrowser)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QColor, QPainter, QTextDocument
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

from database import Client
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency,
                    format_quantity)
import datetime
import re

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, Styled<PERSON>abel)

class ClientsWidget(QWidget):
    """واجهة إدارة العملاء"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للفواتير
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("🤝 إدارة العملاء المتطورة - نظام شامل ومتقدم لإدارة العملاء مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للفواتير
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث باسم العميل، الهاتف، البريد الإلكتروني أو العنوان...")
        self.search_edit.textChanged.connect(self.filter_clients)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_clients)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        main_layout.addWidget(top_frame)

        # إنشاء إطار الجدول والأزرار مطابق للفواتير
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
            }
        """)

        table_layout = QVBoxLayout()
        table_layout.setContentsMargins(5, 5, 5, 5)
        table_layout.setSpacing(5)

        # إنشاء الجدول مطابق للفواتير
        styled_table = StyledTable()
        self.clients_table = styled_table.table
        self.clients_table.setColumnCount(7)
        self.clients_table.setHorizontalHeaderLabels([
            "🆔 الرقم", "👤 اسم العميل", "📞 الهاتف", "📧 البريد الإلكتروني",
            "📍 العنوان", "💰 الرصيد", "🎯 الحالة"
        ])

        # تعيين عرض الأعمدة
        header = self.clients_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # الرقم
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # الاسم
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الهاتف
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # البريد
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # العنوان
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الرصيد
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الحالة

        # إعدادات الجدول
        self.clients_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.clients_table.setSelectionMode(QTableWidget.SingleSelection)
        self.clients_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.clients_table.setAlternatingRowColors(True)
        self.clients_table.setSortingEnabled(True)

        # إضافة الجدول للتخطيط
        table_layout.addWidget(self.clients_table)

        # إنشاء أزرار التحكم مطابقة للفواتير
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # أزرار العمليات الأساسية
        self.add_button = StyledButton("➕ إضافة عميل", "success", "normal")
        self.add_button.clicked.connect(self.add_client)

        self.edit_button = StyledButton("✏️ تعديل", "primary", "normal")
        self.edit_button.clicked.connect(self.edit_client)

        self.delete_button = StyledButton("🗑️ حذف", "danger", "normal")
        self.delete_button.clicked.connect(self.delete_client)

        self.refresh_button = StyledButton("🔄 تحديث", "secondary", "normal")
        self.refresh_button.clicked.connect(self.refresh_data)

        # أزرار التقارير والتصدير
        self.reports_button = StyledButton("📊 تقارير", "info", "normal")
        self.reports_button.clicked.connect(self.show_reports)

        self.export_button = StyledButton("📤 تصدير", "warning", "normal")
        self.export_button.clicked.connect(self.export_data)

        self.print_button = StyledButton("🖨️ طباعة", "info", "normal")
        self.print_button.clicked.connect(self.print_data)

        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(self.add_button.button)
        buttons_layout.addWidget(self.edit_button.button)
        buttons_layout.addWidget(self.delete_button.button)
        buttons_layout.addWidget(self.refresh_button.button)
        buttons_layout.addStretch()  # مساحة فارغة
        buttons_layout.addWidget(self.reports_button.button)
        buttons_layout.addWidget(self.export_button.button)
        buttons_layout.addWidget(self.print_button.button)

        table_layout.addLayout(buttons_layout)

        # إضافة العلامة المائية مطابقة للفواتير
        watermark_label = QLabel("Smart Finish")
        watermark_label.setStyleSheet("""
            QLabel {
                color: rgba(96, 165, 250, 0.15);
                font-size: 180px;
                font-weight: 900;
                font-family: 'Arial Black', Arial, sans-serif;
                background: transparent;
                border: none;
                text-align: center;
                letter-spacing: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            }
        """)
        watermark_label.setAlignment(Qt.AlignCenter)
        watermark_label.setAttribute(Qt.WA_TransparentForMouseEvents)
        watermark_label.lower()

        # تعيين التخطيط للإطار
        table_frame.setLayout(table_layout)
        main_layout.addWidget(table_frame)

        # إضافة العلامة المائية فوق الجدول
        table_layout.addWidget(watermark_label, 0, Qt.AlignCenter)
        watermark_label.lower()

        self.setLayout(main_layout)

        # تحميل البيانات
        self.refresh_data()

    def refresh_data(self):
        """تحديث بيانات الجدول"""
        try:
            # جلب جميع العملاء من قاعدة البيانات
            clients = self.session.query(Client).order_by(Client.created_at.desc()).all()
            self.populate_table(clients)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")

    def populate_table(self, clients):
        """ملء الجدول بالبيانات"""
        try:
            self.clients_table.setRowCount(len(clients))

            for row, client in enumerate(clients):
                # الرقم
                id_item = QTableWidgetItem(str(client.id))
                id_item.setTextAlignment(Qt.AlignCenter)
                self.clients_table.setItem(row, 0, id_item)

                # اسم العميل
                name_item = QTableWidgetItem(client.name or "")
                name_item.setTextAlignment(Qt.AlignCenter)
                self.clients_table.setItem(row, 1, name_item)

                # الهاتف
                phone_item = QTableWidgetItem(client.phone or "")
                phone_item.setTextAlignment(Qt.AlignCenter)
                self.clients_table.setItem(row, 2, phone_item)

                # البريد الإلكتروني
                email_item = QTableWidgetItem(client.email or "")
                email_item.setTextAlignment(Qt.AlignCenter)
                self.clients_table.setItem(row, 3, email_item)

                # العنوان
                address_item = QTableWidgetItem(client.address or "")
                address_item.setTextAlignment(Qt.AlignCenter)
                self.clients_table.setItem(row, 4, address_item)

                # الرصيد
                balance_item = QTableWidgetItem(format_currency(client.balance or 0))
                balance_item.setTextAlignment(Qt.AlignCenter)
                self.clients_table.setItem(row, 5, balance_item)

                # الحالة
                status = self.get_client_status(client.balance or 0)
                status_item = QTableWidgetItem(status)
                status_item.setTextAlignment(Qt.AlignCenter)
                self.clients_table.setItem(row, 6, status_item)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء عرض البيانات: {str(e)}")

    def get_client_status(self, balance):
        """تحديد حالة العميل بناءً على الرصيد"""
        if balance > 0:
            return "🟢 نشط"
        elif balance == 0:
            return "🟡 عادي"
        else:
            return "🔴 مدين"

    def filter_clients(self):
        """تصفية العملاء بناءً على نص البحث"""
        search_text = self.search_edit.text().strip().lower()

        if not search_text:
            self.refresh_data()
            return

        try:
            # البحث في قاعدة البيانات
            clients = self.session.query(Client).filter(
                Client.name.like(f"%{search_text}%") |
                Client.phone.like(f"%{search_text}%") |
                Client.email.like(f"%{search_text}%") |
                Client.address.like(f"%{search_text}%")
            ).order_by(Client.created_at.desc()).all()

            self.populate_table(clients)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    # الدوال الأساسية (سيتم إضافتها لاحقاً)
    def add_client(self):
        """إضافة عميل جديد"""
        pass

    def edit_client(self):
        """تعديل عميل"""
        pass

    def delete_client(self):
        """حذف عميل"""
        pass

    def show_reports(self):
        """عرض التقارير"""
        pass

    def export_data(self):
        """تصدير البيانات"""
        pass

    def print_data(self):
        """طباعة البيانات"""
        pass
